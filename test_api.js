// Test script for Discord Bot Platform API
// Using built-in fetch (Node.js 18+)

const API_BASE = 'http://localhost:3000/api';
let authToken = null;

// Test functions
async function testHealthCheck() {
    console.log('🔍 Testing health check...');
    try {
        const response = await fetch(`${API_BASE}/health`);
        const data = await response.json();
        console.log('✅ Health check passed:', data);
        return true;
    } catch (error) {
        console.error('❌ Health check failed:', error.message);
        return false;
    }
}

async function testRegister() {
    console.log('🔍 Testing user registration...');
    try {
        const response = await fetch(`${API_BASE}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'testuser',
                email: '<EMAIL>',
                password: 'testpassword123'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            console.log('✅ Registration successful:', data.user);
            return true;
        } else {
            console.log('⚠️ Registration failed (might be expected if user exists):', data.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Registration error:', error.message);
        return false;
    }
}

async function testLogin() {
    console.log('🔍 Testing user login...');
    try {
        const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'testuser',
                password: 'testpassword123'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            console.log('✅ Login successful:', data.user);
            return true;
        } else {
            console.error('❌ Login failed:', data.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Login error:', error.message);
        return false;
    }
}

async function testGetUser() {
    console.log('🔍 Testing get user info...');
    try {
        const response = await fetch(`${API_BASE}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Get user info successful:', data.user);
            return true;
        } else {
            console.error('❌ Get user info failed:', data.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Get user info error:', error.message);
        return false;
    }
}

async function testCreateBot() {
    console.log('🔍 Testing bot creation...');
    try {
        const response = await fetch(`${API_BASE}/bot/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                botName: 'Test Bot',
                botToken: 'fake.token.for.testing',
                description: 'A test bot created by API test'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Bot creation successful, Bot ID:', data.botId);
            return data.botId;
        } else {
            console.error('❌ Bot creation failed:', data.error);
            return null;
        }
    } catch (error) {
        console.error('❌ Bot creation error:', error.message);
        return null;
    }
}

async function testGetBots() {
    console.log('🔍 Testing get bots...');
    try {
        const response = await fetch(`${API_BASE}/bot/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Get bots successful, found', data.bots.length, 'bots');
            return data.bots;
        } else {
            console.error('❌ Get bots failed:', data.error);
            return [];
        }
    } catch (error) {
        console.error('❌ Get bots error:', error.message);
        return [];
    }
}

async function testUpdateBotCode(botId) {
    console.log('🔍 Testing bot code update...');
    try {
        const testCode = `
client.on('ready', () => {
    console.log('Test bot is online!');
});

client.on('messageCreate', (message) => {
    if (message.author.bot) return;
    
    if (message.content === '!test') {
        message.reply('Test command works!');
        incrementCommandCount();
    }
});
        `;
        
        const response = await fetch(`${API_BASE}/bot/${botId}/code`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({ code: testCode })
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Bot code update successful');
            return true;
        } else {
            console.error('❌ Bot code update failed:', data.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Bot code update error:', error.message);
        return false;
    }
}

async function testGetTemplates() {
    console.log('🔍 Testing get templates...');
    try {
        const response = await fetch(`${API_BASE}/templates`);
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Get templates successful, found', data.templates.length, 'templates');
            data.templates.forEach(template => {
                console.log(`  - ${template.name}: ${template.description}`);
            });
            return true;
        } else {
            console.error('❌ Get templates failed:', data.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Get templates error:', error.message);
        return false;
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting Discord Bot Platform API Tests\n');
    
    let passed = 0;
    let total = 0;
    
    // Test 1: Health check
    total++;
    if (await testHealthCheck()) passed++;
    console.log('');
    
    // Test 2: Register (might fail if user exists)
    total++;
    await testRegister();
    console.log('');
    
    // Test 3: Login
    total++;
    if (await testLogin()) passed++;
    console.log('');
    
    // Test 4: Get user info
    total++;
    if (await testGetUser()) passed++;
    console.log('');
    
    // Test 5: Get templates
    total++;
    if (await testGetTemplates()) passed++;
    console.log('');
    
    // Test 6: Create bot
    total++;
    const botId = await testCreateBot();
    if (botId) passed++;
    console.log('');
    
    // Test 7: Get bots
    total++;
    if (await testGetBots()) passed++;
    console.log('');
    
    // Test 8: Update bot code (if bot was created)
    if (botId) {
        total++;
        if (await testUpdateBotCode(botId)) passed++;
        console.log('');
    }
    
    console.log(`📊 Test Results: ${passed}/${total} tests passed`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! API is working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Check the logs above for details.');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests };
