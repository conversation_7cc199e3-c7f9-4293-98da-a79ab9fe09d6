#!/bin/bash

echo "Starting Discord Bot Platform..."
echo

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    echo
fi

# Check if database exists
if [ ! -f "database/bot_platform.db" ]; then
    echo "Initializing database..."
    npm run init-db
    echo
fi

# Start the server
echo "Starting server on http://localhost:3000"
echo "Press Ctrl+C to stop the server"
echo
npm start
