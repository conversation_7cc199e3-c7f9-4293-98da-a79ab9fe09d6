# Hướng dẫn sử dụng Discord Bot Platform

## Bước 1: Tạo Discord Bot

### 1.1. <PERSON><PERSON><PERSON> cập Discord Developer Portal
1. <PERSON><PERSON> tới https://discord.com/developers/applications
2. Đăng nhập với tài khoản Discord của bạn
3. Click "New Application"
4. Đặt tên cho application (ví dụ: "My Custom Bot")

### 1.2. Tạo Bot
1. Trong application vừa tạo, click tab "Bot" ở sidebar
2. Click "Add Bot"
3. Tùy chỉnh tên bot và avatar nếu muốn
4. Copy "Token" (giữ bí mật token này!)

### 1.3. <PERSON><PERSON><PERSON> quyền cho Bot
1. Vào tab "OAuth2" > "URL Generator"
2. Chọn scope: "bot"
3. Chọn bot permissions cần thiết:
   - Send Messages
   - Read Message History
   - Use Slash Commands
   - Manage Messages (nếu cần moderation)
   - Kick Members (nếu cần moderation)
4. Copy URL được generate và mở trong browser
5. Chọn server để invite bot vào

## Bước 2: Sử dụng Platform

### 2.1. <PERSON><PERSON><PERSON> ký tài khoản
1. Mở http://localhost:3000
2. Click tab "Register"
3. Nhập username, email, password
4. Click "Register"

### 2.2. Tạo Bot trên Platform
1. Sau khi đăng nhập, click tab "Create Bot"
2. Nhập thông tin:
   - Bot Name: Tên hiển thị
   - Discord Bot Token: Token từ bước 1.2
   - Description: Mô tả (tùy chọn)
3. Click "Create Bot"

### 2.3. Viết Code cho Bot
1. Trong tab "My Bots", click "Edit" trên bot vừa tạo
2. Viết code JavaScript trong editor
3. Click "Save Code"
4. Click "Start Bot" để chạy

## Bước 3: Ví dụ Code Bot

### 3.1. Bot cơ bản
```javascript
client.on('ready', () => {
    console.log(`Bot ${client.user.tag} is online!`);
});

client.on('messageCreate', (message) => {
    if (message.author.bot) return;
    
    if (message.content === '!hello') {
        message.reply('Hello! I am a custom bot!');
        incrementCommandCount();
    }
    
    if (message.content.startsWith('!echo ')) {
        const text = message.content.slice(6);
        message.channel.send(text);
        incrementCommandCount();
        incrementMessageCount();
    }
});
```

### 3.2. Bot với nhiều commands
```javascript
client.on('ready', () => {
    console.log(`Bot ${client.user.tag} is online!`);
});

client.on('messageCreate', (message) => {
    if (message.author.bot) return;
    if (!message.content.startsWith('!')) return;
    
    const args = message.content.slice(1).split(' ');
    const command = args.shift().toLowerCase();
    
    switch(command) {
        case 'ping':
            message.reply(`Pong! Latency: ${client.ws.ping}ms`);
            incrementCommandCount();
            break;
            
        case 'userinfo':
            const user = message.mentions.users.first() || message.author;
            message.reply(`User: ${user.tag}\nID: ${user.id}\nCreated: ${user.createdAt}`);
            incrementCommandCount();
            break;
            
        case 'serverinfo':
            const guild = message.guild;
            message.reply(`Server: ${guild.name}\nMembers: ${guild.memberCount}\nCreated: ${guild.createdAt}`);
            incrementCommandCount();
            break;
    }
});
```

### 3.3. Bot moderation
```javascript
client.on('ready', () => {
    console.log(`Moderation bot ${client.user.tag} is online!`);
});

client.on('messageCreate', async (message) => {
    if (message.author.bot) return;
    if (!message.content.startsWith('!')) return;
    
    const args = message.content.slice(1).split(' ');
    const command = args.shift().toLowerCase();
    
    // Check permissions
    const hasPermission = message.member.permissions.has('MANAGE_MESSAGES');
    
    switch(command) {
        case 'clear':
            if (!hasPermission) {
                message.reply('You need Manage Messages permission!');
                return;
            }
            let amount = parseInt(args[0]) || 1;
            if (amount > 100) amount = 100;
            
            await message.channel.bulkDelete(amount + 1);
            message.channel.send(`Cleared ${amount} messages!`);
            incrementCommandCount();
            break;
            
        case 'timeout':
            if (!hasPermission) {
                message.reply('You need Manage Messages permission!');
                return;
            }
            const userToTimeout = message.mentions.users.first();
            const duration = parseInt(args[1]) || 60; // seconds
            
            if (userToTimeout) {
                const member = message.guild.members.cache.get(userToTimeout.id);
                await member.timeout(duration * 1000);
                message.reply(`${userToTimeout.tag} has been timed out for ${duration} seconds`);
                incrementCommandCount();
            }
            break;
    }
});
```

## Bước 4: Monitoring và Debugging

### 4.1. Xem Logs
- Trong Bot Editor, phần "Bot Logs" hiển thị real-time logs
- Logs bao gồm: info, warn, error, debug
- Click "Refresh Logs" để cập nhật
- Click "Clear Logs" để xóa logs cũ

### 4.2. Bot Controls
- **Start Bot**: Khởi động bot
- **Stop Bot**: Dừng bot
- **Restart Bot**: Restart bot (hữu ích khi update code)

### 4.3. Debugging Tips
1. Sử dụng `console.log()` để debug
2. Kiểm tra logs nếu bot không hoạt động
3. Đảm bảo bot có đủ permissions trong Discord server
4. Kiểm tra token có đúng không

## Bước 5: Best Practices

### 5.1. Security
- Không share bot token với ai
- Không hardcode sensitive data trong code
- Sử dụng environment variables cho production

### 5.2. Code Quality
- Sử dụng try-catch cho async operations
- Validate user input
- Implement rate limiting cho commands
- Sử dụng meaningful variable names

### 5.3. Performance
- Không sử dụng infinite loops
- Cache data khi có thể
- Sử dụng `incrementCommandCount()` và `incrementMessageCount()` để track stats

## Bước 6: Advanced Features

### 6.1. Slash Commands
```javascript
client.on('ready', async () => {
    const commands = [
        {
            name: 'ping',
            description: 'Replies with Pong!'
        },
        {
            name: 'echo',
            description: 'Echoes your message',
            options: [{
                name: 'message',
                description: 'Message to echo',
                type: 3, // STRING
                required: true
            }]
        }
    ];
    
    await client.application.commands.set(commands);
    console.log('Slash commands registered!');
});

client.on('interactionCreate', async (interaction) => {
    if (!interaction.isChatInputCommand()) return;
    
    if (interaction.commandName === 'ping') {
        await interaction.reply('Pong!');
        incrementCommandCount();
    }
    
    if (interaction.commandName === 'echo') {
        const message = interaction.options.getString('message');
        await interaction.reply(message);
        incrementCommandCount();
    }
});
```

### 6.2. Event Handling
```javascript
// Member join
client.on('guildMemberAdd', (member) => {
    const channel = member.guild.channels.cache.find(ch => ch.name === 'welcome');
    if (channel) {
        channel.send(`Welcome ${member.user.tag} to the server!`);
    }
});

// Message reactions
client.on('messageReactionAdd', (reaction, user) => {
    if (user.bot) return;
    console.log(`${user.tag} reacted with ${reaction.emoji.name}`);
});
```

## Troubleshooting

### Common Issues

1. **Bot không online**
   - Kiểm tra token có đúng không
   - Kiểm tra logs có error không
   - Đảm bảo bot đã được invite vào server

2. **Commands không hoạt động**
   - Kiểm tra bot có permission Send Messages không
   - Kiểm tra syntax code có đúng không
   - Xem logs để debug

3. **Bot bị disconnect**
   - Kiểm tra internet connection
   - Token có thể bị revoke
   - Server có thể bị restart

4. **Permission errors**
   - Kiểm tra bot role trong Discord server
   - Đảm bảo bot có đủ permissions cho commands
   - Kiểm tra channel permissions

## Support

Nếu gặp vấn đề, hãy:
1. Kiểm tra logs trong platform
2. Verify bot permissions trong Discord
3. Test với basic commands trước
4. Kiểm tra Discord API status
