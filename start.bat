@echo off
echo Starting Discord Bot Platform...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

REM Check if database exists
if not exist "database\bot_platform.db" (
    echo Initializing database...
    npm run init-db
    echo.
)

REM Start the server
echo Starting server on http://localhost:3000
echo Press Ctrl+C to stop the server
echo.
npm start
